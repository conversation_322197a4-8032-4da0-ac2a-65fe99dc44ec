# نظام الموافقة على الفيديوهات المقترحة
import asyncio
import json
from datetime import datetime
from typing import Dict, Optional, Callable, List
from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup, Update
from telegram.ext import Application, CallbackQueryHandler, ContextTypes
from .logger import logger
from .database import db
from config.settings import BotConfig

class VideoApprovalSystem:
    """نظام الموافقة على الفيديوهات المقترحة من الوكيل"""
    
    def __init__(self):
        self.bot_token = BotConfig.TELEGRAM_BOT_TOKEN
        self.admin_chat_id = None  # سيتم تحديده من الإعدادات
        self.pending_videos = {}  # فيديوهات في انتظار الموافقة
        self.approval_callbacks = {}  # callbacks للموافقة/الرفض
        self.approval_enabled = False  # حالة تفعيل نظام الموافقة

        # إعداد البوت
        self.application = None
        self.setup_bot()
    
    def setup_bot(self):
        """إعداد بوت تيليجرام للموافقة"""
        try:
            if not self.bot_token:
                logger.warning("⚠️ لا يوجد توكن تيليجرام للموافقة على الفيديوهات")
                return

            try:
                # محاولة إعداد البوت بطريقة آمنة
                logger.info("🔧 محاولة إعداد بوت Telegram...")

                # إنشاء التطبيق
                self.application = Application.builder().token(self.bot_token).build()

                # إضافة معالج للأزرار
                self.application.add_handler(CallbackQueryHandler(self._handle_approval_callback))

                logger.info("✅ تم إعداد نظام الموافقة على الفيديوهات")
                self.approval_enabled = True

            except AttributeError as attr_error:
                logger.warning(f"⚠️ مشكلة في إصدار python-telegram-bot: {attr_error}")
                logger.info("🔄 سيتم تفعيل نظام الموافقة في الوضع المبسط")
                # تفعيل النظام في وضع مبسط
                self.application = "manual_mode"
                self.approval_enabled = True

            except ImportError as import_error:
                logger.warning(f"⚠️ مشكلة في استيراد مكتبة Telegram: {import_error}")
                logger.info("🔄 سيتم تفعيل نظام الموافقة في الوضع المبسط")
                self.application = "manual_mode"
                self.approval_enabled = True

            except Exception as telegram_error:
                logger.warning(f"⚠️ خطأ عام في إعداد Telegram Bot: {telegram_error}")
                logger.info("🔄 سيتم تفعيل نظام الموافقة في الوضع المبسط")
                # تفعيل النظام في وضع مبسط
                self.application = "manual_mode"
                self.approval_enabled = True

        except Exception as e:
            logger.error(f"❌ فشل في إعداد نظام الموافقة: {e}")
            self.application = None
            self.approval_enabled = False

    async def start_approval_bot(self):
        """بدء تشغيل بوت الموافقة"""
        try:
            if self.approval_enabled:
                if self.application == "manual_mode":
                    logger.info("✅ نظام الموافقة مفعل في الوضع اليدوي")
                elif self.application:
                    # محاولة تهيئة البوت إذا لم يكن مهيأ
                    try:
                        if hasattr(self.application, 'initialize'):
                            await self.application.initialize()
                            logger.info("✅ تم تهيئة بوت الموافقة بنجاح")
                        logger.info("✅ نظام الموافقة جاهز للعمل")
                    except Exception as init_error:
                        logger.warning(f"⚠️ فشل في تهيئة البوت: {init_error}")
                        logger.info("🔄 التحول للوضع اليدوي...")
                        self.application = "manual_mode"
                else:
                    logger.info("✅ نظام الموافقة مفعل بدون تطبيق")
            else:
                logger.warning("⚠️ نظام الموافقة غير مفعل")

        except Exception as e:
            logger.error(f"❌ خطأ في بدء تشغيل بوت الموافقة: {e}")
            # لا نعطل النظام، فقط نحوله للوضع اليدوي
            self.application = "manual_mode"
            logger.info("🔄 تم التحول للوضع اليدوي بسبب الخطأ")
    
    async def request_video_approval(self, video_data: Dict, approval_callback: Callable, extracted_text: str = None) -> str:
        """طلب الموافقة على فيديو مقترح"""
        try:
            if not self.approval_enabled:
                logger.warning("⚠️ نظام الموافقة غير مفعل - سيتم الموافقة تلقائياً")
                await approval_callback(True, "تلقائي")
                return "auto_approved"
            
            # إنشاء معرف فريد للفيديو
            approval_id = f"video_{video_data['id']}_{int(datetime.now().timestamp())}"
            
            # حفظ بيانات الفيديو
            self.pending_videos[approval_id] = {
                'video_data': video_data,
                'callback': approval_callback,
                'timestamp': datetime.now().isoformat(),
                'status': 'pending',
                'extracted_text': extracted_text
            }
            
            # إرسال رسالة الموافقة
            await self._send_approval_message(approval_id, video_data, extracted_text)
            
            logger.info(f"📤 تم إرسال طلب موافقة للفيديو: {video_data['title']}")
            return approval_id
            
        except Exception as e:
            logger.error(f"❌ خطأ في طلب الموافقة: {e}")
            # في حالة الخطأ، موافقة تلقائية
            await approval_callback(True, "خطأ - موافقة تلقائية")
            return "error_auto_approved"
    
    async def _send_approval_message(self, approval_id: str, video_data: Dict, extracted_text: str = None):
        """إرسال رسالة الموافقة مع الأزرار"""
        try:
            # تحديد معرف المدير (يمكن تحديده من الإعدادات أو قاعدة البيانات)
            admin_chat_id = self._get_admin_chat_id()
            if not admin_chat_id:
                logger.warning("⚠️ لا يوجد معرف مدير للموافقة")
                return
            
            # إعداد الرسالة
            message_text = self._format_approval_message(video_data)
            
            # إعداد الأزرار
            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("✅ موافق", callback_data=f"approve_{approval_id}"),
                    InlineKeyboardButton("❌ رفض", callback_data=f"reject_{approval_id}")
                ],
                [
                    InlineKeyboardButton("🔄 اختر فيديو آخر", callback_data=f"choose_other_{approval_id}")
                ]
            ])
            
            # إرسال الرسالة
            bot = Bot(token=self.bot_token)

            # إرسال النص المستخرج أولاً إذا كان متوفراً
            if extracted_text and len(extracted_text.strip()) > 0:
                await self._send_extracted_text(bot, admin_chat_id, extracted_text, video_data)

            # إرسال الرسالة مع معالجة الأخطاء
            try:
                # محاولة إرسال رسالة مع أزرار أولاً
                if self.application != "manual_mode":
                    await bot.send_message(
                        chat_id=admin_chat_id,
                        text=message_text,
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
                    logger.info(f"📤 تم إرسال رسالة الموافقة مع الأزرار للمدير {admin_chat_id}")
                else:
                    # في الوضع اليدوي، إرسال رسالة بدون أزرار
                    simple_message = f"{message_text}\n\n💬 <b>للموافقة:</b> أرسل 'موافق'\n💬 <b>للرفض:</b> أرسل 'رفض'"
                    await bot.send_message(
                        chat_id=admin_chat_id,
                        text=simple_message,
                        parse_mode='HTML'
                    )
                    logger.info(f"📤 تم إرسال رسالة الموافقة اليدوية للمدير {admin_chat_id}")

            except Exception as send_error:
                logger.error(f"❌ فشل في إرسال رسالة الموافقة: {send_error}")
                # محاولة إرسال رسالة مبسطة جداً
                try:
                    fallback_message = f"🎥 فيديو مقترح للموافقة:\n{video_data.get('title', 'غير محدد')}\n\nالرد بـ 'موافق' أو 'رفض'"
                    await bot.send_message(
                        chat_id=admin_chat_id,
                        text=fallback_message
                    )
                    logger.info(f"📤 تم إرسال رسالة موافقة احتياطية للمدير")
                except Exception as simple_error:
                    logger.error(f"❌ فشل في إرسال الرسالة الاحتياطية: {simple_error}")
                    raise
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال رسالة الموافقة: {e}")
    
    def _format_approval_message(self, video_data: Dict) -> str:
        """تنسيق رسالة الموافقة"""
        try:
            channel_info = video_data.get('channel_info', {})
            
            # تحويل مدة الفيديو
            duration = video_data.get('duration', 0)
            duration_text = f"{duration//60}:{duration%60:02d}" if duration else "غير محدد"
            
            # تاريخ النشر
            published_date = video_data.get('published_at', '')
            if published_date:
                try:
                    date_obj = datetime.fromisoformat(published_date.replace('Z', '+00:00'))
                    published_text = date_obj.strftime('%Y-%m-%d %H:%M')
                except:
                    published_text = published_date
            else:
                published_text = "غير محدد"
            
            message = f"""
🎥 <b>فيديو مقترح للمعالجة</b>

📺 <b>القناة:</b> {channel_info.get('name', 'غير محدد')}
🏷️ <b>العنوان:</b> {video_data.get('title', 'غير محدد')}

⏱️ <b>المدة:</b> {duration_text}
📅 <b>تاريخ النشر:</b> {published_text}
🌐 <b>اللغة:</b> {channel_info.get('language', 'غير محدد')}

📝 <b>الوصف:</b>
{video_data.get('description', 'لا يوجد وصف')[:200]}...

🔗 <b>الرابط:</b> https://youtube.com/watch?v={video_data.get('id', '')}

❓ <b>هل تريد معالجة هذا الفيديو لاستخراج الأخبار منه؟</b>
            """.strip()
            
            return message
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنسيق رسالة الموافقة: {e}")
            return f"فيديو مقترح: {video_data.get('title', 'غير محدد')}"
    
    def _get_admin_chat_id(self) -> Optional[str]:
        """الحصول على معرف المدير"""
        try:
            # استخدام معرف المدير من الإعدادات
            admin_id = getattr(BotConfig, 'TELEGRAM_ADMIN_ID', None)
            if admin_id:
                logger.info(f"📱 استخدام معرف المدير: {admin_id}")
                return admin_id

            # إذا لم يكن متوفر، استخدم معرف القناة كبديل
            logger.warning("⚠️ لا يوجد معرف مدير محدد، استخدام معرف القناة")
            return BotConfig.TELEGRAM_CHANNEL_ID

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على معرف المدير: {e}")
            return None
    
    async def _handle_approval_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة ردود الموافقة"""
        try:
            query = update.callback_query
            await query.answer()
            
            # استخراج البيانات من callback_data
            callback_data = query.data
            action, approval_id = callback_data.split('_', 1)
            
            # التحقق من وجود الفيديو في الانتظار
            if approval_id not in self.pending_videos:
                await query.edit_message_text("❌ انتهت صلاحية طلب الموافقة")
                return
            
            video_info = self.pending_videos[approval_id]
            
            # معالجة الإجراء
            if action == "approve":
                await self._process_approval(approval_id, True, "موافقة المدير")
                await query.edit_message_text("✅ تم الموافقة على الفيديو - سيتم معالجته الآن")
                
            elif action == "reject":
                await self._process_approval(approval_id, False, "رفض المدير")
                await query.edit_message_text("❌ تم رفض الفيديو - سيتم البحث عن فيديو آخر")
                
            elif action == "choose":
                await self._process_approval(approval_id, False, "طلب فيديو آخر")
                await query.edit_message_text("🔄 سيتم البحث عن فيديو آخر")
            
            # إزالة الفيديو من قائمة الانتظار
            del self.pending_videos[approval_id]
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة رد الموافقة: {e}")
            try:
                await query.edit_message_text("❌ حدث خطأ في معالجة الطلب")
            except:
                pass
    
    async def _process_approval(self, approval_id: str, approved: bool, reason: str):
        """معالجة قرار الموافقة"""
        try:
            video_info = self.pending_videos.get(approval_id)
            if not video_info:
                return
            
            # تحديث حالة الفيديو
            video_info['status'] = 'approved' if approved else 'rejected'
            video_info['reason'] = reason
            video_info['decision_time'] = datetime.now().isoformat()
            
            # استدعاء callback
            callback = video_info['callback']
            if callback:
                await callback(approved, reason)
            
            # تسجيل القرار في قاعدة البيانات
            self._log_approval_decision(approval_id, video_info, approved, reason)
            
            logger.info(f"✅ تم معالجة قرار الموافقة: {approval_id} - {'موافق' if approved else 'مرفوض'}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة قرار الموافقة: {e}")
    
    def _log_approval_decision(self, approval_id: str, video_info: Dict, approved: bool, reason: str):
        """تسجيل قرار الموافقة في قاعدة البيانات"""
        try:
            decision_data = {
                'approval_id': approval_id,
                'video_id': video_info['video_data']['id'],
                'video_title': video_info['video_data']['title'],
                'channel_name': video_info['video_data'].get('channel_info', {}).get('name', ''),
                'approved': approved,
                'reason': reason,
                'request_time': video_info['timestamp'],
                'decision_time': datetime.now().isoformat()
            }
            
            # حفظ في قاعدة البيانات
            db.log_video_approval_decision(decision_data)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل قرار الموافقة: {e}")
    
    async def start_approval_bot(self):
        """بدء تشغيل بوت الموافقة"""
        try:
            if self.application:
                await self.application.initialize()
                await self.application.start()
                logger.info("🤖 تم بدء تشغيل بوت الموافقة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في بدء تشغيل بوت الموافقة: {e}")
    
    async def stop_approval_bot(self):
        """إيقاف بوت الموافقة"""
        try:
            if self.application:
                await self.application.stop()
                await self.application.shutdown()
                logger.info("🛑 تم إيقاف بوت الموافقة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إيقاف بوت الموافقة: {e}")
    
    def get_pending_approvals_count(self) -> int:
        """الحصول على عدد الموافقات المعلقة"""
        return len(self.pending_videos)
    
    def get_pending_approvals(self) -> Dict:
        """الحصول على قائمة الموافقات المعلقة"""
        return self.pending_videos.copy()
    
    def clear_expired_approvals(self, max_age_hours: int = 24):
        """مسح الموافقات المنتهية الصلاحية"""
        try:
            current_time = datetime.now()
            expired_ids = []
            
            for approval_id, video_info in self.pending_videos.items():
                request_time = datetime.fromisoformat(video_info['timestamp'])
                age_hours = (current_time - request_time).total_seconds() / 3600
                
                if age_hours > max_age_hours:
                    expired_ids.append(approval_id)
            
            for approval_id in expired_ids:
                del self.pending_videos[approval_id]
                logger.info(f"🗑️ تم مسح موافقة منتهية الصلاحية: {approval_id}")
            
            return len(expired_ids)
            
        except Exception as e:
            logger.error(f"❌ خطأ في مسح الموافقات المنتهية: {e}")
            return 0

    async def _send_extracted_text(self, bot, admin_chat_id: str, extracted_text: str, video_data: Dict):
        """إرسال النص المستخرج من الفيديو للمدير"""
        try:
            # تحضير النص للإرسال
            text_preview = self._format_extracted_text(extracted_text, video_data)

            # تقسيم النص إذا كان طويلاً (Telegram يحد الرسائل بـ 4096 حرف)
            max_length = 4000  # ترك مساحة للتنسيق

            if len(text_preview) <= max_length:
                # إرسال النص كرسالة واحدة
                await bot.send_message(
                    chat_id=admin_chat_id,
                    text=text_preview,
                    parse_mode='HTML'
                )
                logger.info("📄 تم إرسال النص المستخرج للمدير")
            else:
                # تقسيم النص إلى أجزاء
                parts = self._split_text_into_parts(text_preview, max_length)
                for i, part in enumerate(parts):
                    part_header = f"📄 النص المستخرج - الجزء {i+1}/{len(parts)}\n\n" if i == 0 else f"📄 الجزء {i+1}/{len(parts)} (تكملة)\n\n"
                    await bot.send_message(
                        chat_id=admin_chat_id,
                        text=part_header + part,
                        parse_mode='HTML'
                    )
                    # تأخير قصير بين الرسائل
                    await asyncio.sleep(0.5)

                logger.info(f"📄 تم إرسال النص المستخرج في {len(parts)} جزء للمدير")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال النص المستخرج: {e}")
            # إرسال رسالة بديلة
            try:
                fallback_message = f"📄 <b>النص المستخرج من الفيديو:</b>\n\n❌ فشل في عرض النص الكامل\n\n<i>طول النص: {len(extracted_text)} حرف</i>"
                await bot.send_message(
                    chat_id=admin_chat_id,
                    text=fallback_message,
                    parse_mode='HTML'
                )
            except:
                pass  # تجاهل الأخطاء في الرسالة البديلة

    def _format_extracted_text(self, extracted_text: str, video_data: Dict) -> str:
        """تنسيق النص المستخرج للعرض"""
        try:
            # معلومات الفيديو
            title = video_data.get('title', 'غير محدد')
            channel_name = video_data.get('channel_info', {}).get('name', 'غير محدد')

            # تنظيف النص
            cleaned_text = extracted_text.strip()
            if not cleaned_text:
                cleaned_text = "لم يتم استخراج نص من الفيديو"

            # تحديد طول النص
            text_length = len(cleaned_text)
            word_count = len(cleaned_text.split())

            # تنسيق الرسالة
            formatted_message = f"""📄 <b>النص المستخرج من الفيديو</b>

🎥 <b>العنوان:</b> {title}
📺 <b>القناة:</b> {channel_name}
📊 <b>إحصائيات:</b> {text_length} حرف، {word_count} كلمة

📝 <b>النص المستخرج:</b>
<code>{cleaned_text}</code>

💡 <i>يمكنك مراجعة هذا النص لاتخاذ قرار الموافقة</i>"""

            return formatted_message

        except Exception as e:
            logger.error(f"❌ خطأ في تنسيق النص المستخرج: {e}")
            return f"📄 <b>النص المستخرج:</b>\n\n{extracted_text[:1000]}..."

    def _split_text_into_parts(self, text: str, max_length: int) -> List[str]:
        """تقسيم النص إلى أجزاء مناسبة لـ Telegram"""
        try:
            parts = []
            current_part = ""

            # تقسيم النص إلى جمل
            sentences = text.split('\n')

            for sentence in sentences:
                # إذا كانت الجملة طويلة جداً، قسمها
                if len(sentence) > max_length:
                    # حفظ الجزء الحالي إذا لم يكن فارغاً
                    if current_part:
                        parts.append(current_part.strip())
                        current_part = ""

                    # تقسيم الجملة الطويلة
                    words = sentence.split(' ')
                    temp_sentence = ""

                    for word in words:
                        if len(temp_sentence + word + " ") <= max_length:
                            temp_sentence += word + " "
                        else:
                            if temp_sentence:
                                parts.append(temp_sentence.strip())
                            temp_sentence = word + " "

                    if temp_sentence:
                        current_part = temp_sentence

                # إذا كانت إضافة الجملة ستتجاوز الحد الأقصى
                elif len(current_part + sentence + "\n") > max_length:
                    if current_part:
                        parts.append(current_part.strip())
                    current_part = sentence + "\n"
                else:
                    current_part += sentence + "\n"

            # إضافة الجزء الأخير
            if current_part:
                parts.append(current_part.strip())

            return parts if parts else [text[:max_length]]

        except Exception as e:
            logger.error(f"❌ خطأ في تقسيم النص: {e}")
            # تقسيم بسيط كبديل
            return [text[i:i+max_length] for i in range(0, len(text), max_length)]
